# AnimLibFlipbookItem 内存管理使用指南

## 概述

`AnimLibFlipbookItem` 类现在包含了完整的内存管理和资源清理功能，以防止内存泄漏和确保资源的正确释放。

## 新增功能

### 1. `cleanup()` 方法
用于手动清理所有资源和断开信号连接。

```python
# 创建 flipbook item
flipbook_item = AnimLibFlipbookItem(io_data=your_data)

# 使用完毕后手动清理
flipbook_item.cleanup()
```

### 2. `__del__()` 析构方法
当对象被垃圾回收时自动调用清理功能。

```python
# 对象超出作用域时会自动调用 __del__
def some_function():
    flipbook_item = AnimLibFlipbookItem(io_data=your_data)
    # ... 使用 flipbook_item
    # 函数结束时，flipbook_item 会自动清理
```

### 3. 改进的 `destruct()` 方法
现在会先调用 `cleanup()` 清理资源，然后删除文件。

```python
# 完全销毁 flipbook item（包括文件）
flipbook_item.destruct()
```

## 清理的资源

新的清理功能会处理以下资源：

1. **Qt 定时器** (`_timer`)
   - 停止定时器
   - 断开信号连接
   - 删除定时器对象

2. **信号连接**
   - 断开 `frameChanged` 信号的所有连接

3. **缓存数据**
   - 清空 `animation_cache` 字典
   - 清空 `annotation_cache` OrderedDict

4. **图像资源**
   - 清空 `_frames` 列表
   - 重置 `_current_display`

5. **实例变量重置**
   - 重置所有状态变量到初始值

## 使用建议

### 最佳实践

1. **手动清理（推荐）**
```python
flipbook_item = AnimLibFlipbookItem(io_data=data)
try:
    # 使用 flipbook_item
    flipbook_item.load_flipbook()
    flipbook_item.play_flipbook_animation()
finally:
    # 确保资源被清理
    flipbook_item.cleanup()
```

2. **使用上下文管理器模式**
```python
class FlipbookContext:
    def __init__(self, io_data):
        self.flipbook_item = AnimLibFlipbookItem(io_data)
    
    def __enter__(self):
        return self.flipbook_item
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.flipbook_item.cleanup()

# 使用方式
with FlipbookContext(your_data) as flipbook:
    flipbook.load_flipbook()
    flipbook.play_flipbook_animation()
# 自动清理
```

3. **批量处理时的清理**
```python
flipbook_items = []
try:
    for data in flipbook_data_list:
        item = AnimLibFlipbookItem(data)
        flipbook_items.append(item)
        # 处理 item
finally:
    # 清理所有 items
    for item in flipbook_items:
        item.cleanup()
```

### 注意事项

1. **避免重复清理**
   - `cleanup()` 方法可以安全地多次调用
   - 内部有异常处理，不会因为重复清理而崩溃

2. **清理后的使用**
   - 调用 `cleanup()` 后，对象不应再被使用
   - 大部分属性会被设置为 `None` 或重置为初始值

3. **文件删除**
   - 只有 `destruct()` 方法会删除磁盘文件
   - `cleanup()` 只清理内存资源，不删除文件

## 错误处理

所有清理方法都包含异常处理：

```python
try:
    flipbook_item.cleanup()
except Exception as e:
    print("清理过程中出现错误: %s" % str(e))
    # 错误不会阻止程序继续运行
```

## 调试信息

清理方法会输出调试信息：

```
[AnimLibFlipbookItem] Resources cleaned up successfully
[AnimLibFlipbookItem] Error during cleanup: <error_message>
[AnimLibFlipbookItem] Error in destructor: <error_message>
[AnimLibFlipbookItem] Error removing flipbook folder: <error_message>
```

## 兼容性

- 新功能完全向后兼容
- 现有代码无需修改即可继续工作
- 建议在新代码中使用 `cleanup()` 方法进行资源管理

## 性能影响

- 清理操作非常轻量，对性能影响微乎其微
- 可以显著减少内存使用和防止内存泄漏
- 特别适合长时间运行的应用程序或处理大量 flipbook 的场景
