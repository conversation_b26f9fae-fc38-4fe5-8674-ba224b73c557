#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Test script for AnimLibFlipbookItem cleanup functionality.
This script tests the new cleanup(), __del__(), and improved destruct() methods.
"""

import sys
import os
import tempfile
import shutil
from collections import OrderedDict

# Add the anim-lib path to sys.path for testing
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'py'))

try:
    from lsr.anim_lib.ui.preview.item import AnimLibFlipbookItem
    from lsr.anim_lib.data import constant
    print("Successfully imported AnimLibFlipbookItem")
except ImportError as e:
    print("Import error: %s" % str(e))
    print("This test requires the full anim-lib environment to run properly.")
    sys.exit(1)


def test_cleanup_functionality():
    """Test the cleanup functionality of AnimLibFlipbookItem"""
    print("\n=== Testing AnimLibFlipbookItem Cleanup Functionality ===")
    
    # Create a temporary directory for testing
    temp_dir = tempfile.mkdtemp(prefix="flipbook_test_")
    print("Created temporary directory: %s" % temp_dir)
    
    try:
        # Create test data structure
        test_io_data = {
            "flipbook_sequence_folder": temp_dir,
            "flipbook_thumbnail_file": "",
            "animation_data_file": "",
            "annotation_data_file": ""
        }
        
        # Create some test files in the temp directory
        for i in range(3):
            test_file = os.path.join(temp_dir, "frame_%03d.jpg" % i)
            with open(test_file, 'w') as f:
                f.write("test frame %d" % i)
        
        print("Created test flipbook item...")
        
        # Create AnimLibFlipbookItem instance
        flipbook_item = AnimLibFlipbookItem(io_data=test_io_data)
        
        # Set up some test data
        flipbook_item.animation_cache = {"test": "data"}
        flipbook_item.annotation_cache = OrderedDict([("key1", "value1"), ("key2", "value2")])
        flipbook_item._frames = ["frame1", "frame2", "frame3"]
        flipbook_item._current_display = "test_display"
        
        print("Set up test data in flipbook item")
        
        # Test cleanup method
        print("\nTesting cleanup() method...")
        flipbook_item.cleanup()
        
        # Verify cleanup worked
        assert flipbook_item.animation_cache is None, "animation_cache should be None after cleanup"
        assert flipbook_item.annotation_cache is None, "annotation_cache should be None after cleanup"
        assert flipbook_item._frames is None, "frames should be None after cleanup"
        assert flipbook_item._current_display is None, "current_display should be None after cleanup"
        assert flipbook_item._timer is None, "timer should be None after cleanup"
        
        print("✓ cleanup() method works correctly")
        
        # Test destruct method (this will also test cleanup again)
        print("\nTesting destruct() method...")
        flipbook_item.destruct()
        
        # Verify files were removed
        assert not os.path.exists(temp_dir), "Temporary directory should be removed after destruct"
        
        print("✓ destruct() method works correctly")
        
        print("\n=== All tests passed! ===")
        
    except Exception as e:
        print("Test failed with error: %s" % str(e))
        # Clean up temp directory if test failed
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        raise
    
    finally:
        # Clean up temp directory if it still exists
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)


def test_destructor():
    """Test the __del__ destructor method"""
    print("\n=== Testing __del__ Destructor ===")
    
    temp_dir = tempfile.mkdtemp(prefix="flipbook_destructor_test_")
    
    try:
        test_io_data = {
            "flipbook_sequence_folder": temp_dir,
            "flipbook_thumbnail_file": "",
            "animation_data_file": "",
            "annotation_data_file": ""
        }
        
        # Create flipbook item in a scope that will trigger __del__
        def create_and_destroy_item():
            item = AnimLibFlipbookItem(io_data=test_io_data)
            item.animation_cache = {"test": "destructor"}
            item._frames = ["test_frame"]
            return item
        
        print("Creating and destroying flipbook item to test __del__...")
        item = create_and_destroy_item()
        
        # Force garbage collection to trigger __del__
        import gc
        del item
        gc.collect()
        
        print("✓ __del__ destructor executed without errors")
        
    finally:
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)


if __name__ == "__main__":
    try:
        test_cleanup_functionality()
        test_destructor()
        print("\n🎉 All AnimLibFlipbookItem cleanup tests completed successfully!")
        
    except Exception as e:
        print("\n❌ Tests failed: %s" % str(e))
        sys.exit(1)
