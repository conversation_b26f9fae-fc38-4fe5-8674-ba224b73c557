"""
Scroll Limb
"""

import maya.cmds as cmds
import lsr.maya.maya_math as mmath
import lsr.protostar.core.parameter as pa
from lsr.maya.nodezoo.node import Joint
from lsr.maya.nodezoo.node import InterpolateMatrix
from lsr.maya.nodezoo.node import MultiplyMatrix
from lsr.maya.standard.name import NodeName
from lsr.maya.rig.meta_limb import MetaLimb
import lsr.maya.rig.constants as const


class ScrollLimb(MetaLimb):
    """Scroll limb class

    :limb type: ScrollLimb
    """

    _LIMB_TYPE = 'ScrollLimb'

    _UI_ICON = 'scroll'

    _INPUT_SKEL_TYPE = const.InputSkelType.multi_chain

    # --- input parameters

    @pa.list_param(item_type='list')
    def input_skeleton(self):
        """Input skeleton."""

    @pa.int_param(default=10, min_value=1)
    def num_segments(self):
        """The number of joints."""

    @pa.list_param(item_type='str', default=('PLC', 'SDK', 'OFFSET'))
    def group_exts(self):
        """Ctrl group extensions."""

    # --- end of parameter definition

    def marker_data(self):
        """Skip marker data as this limb depends on a pre-built
        joint hierarchy."""
        return

    def limb_template_data(self, *args, **kwargs):
        """limb template data"""
        limb_name = kwargs.get('name_part', 'scroll')
        name_side = kwargs.get('name_side', 'M')
        count = self.num_segments.value
        cmds.select(deselect=True)
        joint_chain = []
        x_values = [0, 10]
        for i in range(2):
            x = x_values[i]
            jt_node = Joint.create(
                name='{}_{:02d}_{}_RIGJNT'.format(limb_name, i, name_side),
                p=(x, 0, 0))
            joint_chain.append(jt_node)
            cmds.select(deselect=True)
        joint_chain[0].orient_chain()
        cmds.select(deselect=True)
        return joint_chain

    def run(self):
        """Builds the limb ctrl rig."""
        self.negate = self.side.enum_value == "R"
        self.add_objects()
        self.create_joint_structure(ctrls=self.fk_ctrls, set_hierarchy=False)
        self.add_operators()

    def set_joint_parent_structure(self):
        """override parent class method"""
        socket = self.get_parent_socket()
        if not (self.joint_list and socket):
            return

        for jnt in self.joint_list:
            cmds.parent(jnt, socket, relative=True)

            lsr_const = jnt.list_connections(
                type='lsr_transformConstraint',
                source=True,
                destination=False,
                plugs=False)
            if lsr_const:
                cns_m = lsr_const[0]
                cns_m.rotate.disconnect(jnt.rotate)
                jnt.make_identity(apply=True, rotate=True)
                cns_m.driverRotationOffset.value = jnt.jointOrient.value
                cns_m.rotate >> jnt.rotate

    def add_objects(self):
        """Add Rigging objects"""
        group_exts = self.group_exts.value
        pos_offset = [0, 0, 0]

        bone_01 = Joint(self.rig_skeleton[0][0])
        bone_02 = Joint(self.rig_skeleton[1][0])

        self.master_ctrl = self.add_ctrl(
            name=NodeName(self.long_name, desc='mst', ext='CTRL'),
            shape='cube',
            pos=pos_offset,
            rot=(0, 0, 0),
            color=(0, 0, 1),
            group_exts=group_exts)

        mat_01 = bone_01.get_matrix(space='world', as_tuple=False).asMatrix()
        mat_02 = bone_02.get_matrix(space='world', as_tuple=False).asMatrix()

        bone_mat_c = mmath.interpolate_two_matrix(mat_01, mat_02, 0.5)
        self.master_ctrl.get_group(group_exts[0]).set_matrix(bone_mat_c, space='world')
        # self.fk_ctrls.append(self.master_ctrl)

        count = self.num_segments.value
        step = 1.0 / (count-1)
        start_ctrl = None
        end_ctrl = None
        for i in range(count):
            ctrl = self.add_ctrl(
                name=NodeName(self.long_name, num=i, ext='FKCTRL'),
                shape='circle',
                pos=pos_offset,
                rot=(0, 0, 90),
                group_exts=group_exts,
                parent=self.master_ctrl
            )

            mat_c = mmath.interpolate_two_matrix(mat_02, mat_01, step*i)
            ctrl.get_group(group_exts[0]).set_matrix(mat_c, space='world')
            ctrl.set_matrix(mat_c, space='world')
            self.fk_ctrls.append(ctrl)
            if i == 0:
                start_ctrl = ctrl
            if i == count - 1:
                end_ctrl = ctrl

        self.micro_ctrls = self.fk_ctrls[1:-1]
        for i, ctrl in enumerate(self.micro_ctrls):
            ctrl.shape.color.value = (1, 0, 0)
            inter_matrix = InterpolateMatrix.create(
                ctrl,
                objA=start_ctrl,
                objB=end_ctrl
            )
            plc_node = ctrl.get_group(group_exts[0])
            blend_value = (i+1) * step

            dep_mat = inter_matrix.create_decomposeMatrix(
                obj=plc_node,
                blend=min(1.0, blend_value),
                rotate=True,
                translate=True
            )

            mul_matrix = MultiplyMatrix.create(inter_matrix,
                                               attr_A=inter_matrix.output,
                                               attr_B=plc_node.parentInverseMatrix)

            mul_matrix.outputMatrix >> dep_mat.inputMatrix

        self.start_end_ctrls = [start_ctrl, end_ctrl]

    def add_operators(self):
        """Add operators"""

        group_exts = self.group_exts.value

        micro_vis = self.add_limb_attr('bool', name='micro_vis', keyable=False, defaultValue=True)
        close_open = self.master_ctrl.add_attr(
            'float', name='close_open',  defaultValue=1.0,
            minValue=0, maxValue=1, keyable=True
        )

        for ctrl in self.micro_ctrls:
            plc_node = ctrl.get_group(group_exts[0])
            micro_vis >> plc_node.visibility

        for ctrl in self.start_end_ctrls:
            plc_node = ctrl.get_group(group_exts[0])
            pin_name = NodeName(plc_node).replace_desc('ORI').replace_ext('POS')
            ori_pos_node = plc_node.duplicate(name=pin_name, parentOnly=True)[0]
            ori_pos_node.hiddenInOutliner.value = True

            mul_matrix_01_name = NodeName(ctrl).replace_desc('MULA')
            mul_matrix_01 = MultiplyMatrix.create(
                mul_matrix_01_name,
                objA=self.master_ctrl,
                objB=plc_node,
                parentInverse=True
            )

            mul_matrix_02_name = NodeName(ctrl).replace_desc('MULB')
            mul_matrix_02 = MultiplyMatrix.create(
                mul_matrix_02_name,
                objA=ori_pos_node,
                objB=plc_node,
                parentInverse=True
            )

            inter_matrix = InterpolateMatrix.create(ctrl, attr_A=mul_matrix_01.output, attr_B=mul_matrix_02.output)
            inter_matrix.create_decomposeMatrix(obj=plc_node, blend=min(1.0, 0), rotate=True, translate=True)
            close_open >> inter_matrix.blend

        self.ws_root.inheritsTransform.value = True

    def end(self):
        """Cleans up this limb, trigger limb connection methods.
        If parameter `mirror` is set to True, build the mirrored limb.

        It executes after self.run() is called.
        """
        super(ScrollLimb, self).end()
        self.ctrl_leaf.set_parent(self.limb_root.value)
        self.ws_root.visibility.value = False

        for ctrl in self.fk_ctrls:
            ctrl.lock('s')
        self.master_ctrl.lock('s')
