"""
Export Animation action of ES_Game
"""

import os
import re

from maya import cmds
from maya import mel

from lsr.maya.nodezoo.node import Node
from lsr.maya.userlib.actions.exporter_animation import ExportAnimation
import lsr.protostar.core.parameter as pa
import lsr.maya.animtools.fbx_exporter.export_utils as utils
import lsr.protostar.core.exception as exp
import lsr.maya.rig.rig_global as rg
from lsr.fbxsdk.FBX_Scene import FBX_Class


class Arena_exp_anim(ExportAnimation):
    """
    ES Export Animation
    """
    # --- input parameters

    @pa.bool_param(default=True)
    def export_body(self):
        """If True, enable body animation export."""

    @pa.bool_param(default=True)
    def export_face(self):
        """If True, enable face animation export."""

    @pa.bool_param(default=True)
    def export_prop(self):
        """If True, enable prop(weapon) animation export."""

    @pa.bool_param(default=True)
    def export_prop_origin(self):
        """If True, export prop(weapon) at origin point."""

    def run(self):
        """
        ES Anim Export run method
        """
        super(Arena_exp_anim, self).run()

        rig_roots = cmds.ls(
            'RIG.{}'.format('rig_template'),
            objectsOnly=True, recursive=True) or []

        exported_bone_nodes = []
        prop_bone_nodes = []
        self.root_rig_dict = {}

        for rig_node in rig_roots:
            rg_node = rg.RigGlobal(rig_node)

            if self.export_body.value:
                if rg_node.rig_type == 'body':
                    exported_bone_nodes.append(rg_node.export_bone_grp)
                    self.root_rig_dict[rg_node.export_bone_grp.name] = rg_node

            if self.export_face.value:
                if rg_node.rig_type == 'face':
                    exported_bone_nodes.append(rg_node.export_bone_grp)
                    self.root_rig_dict[rg_node.export_bone_grp.name] = rg_node

            if self.export_prop.value:
                if rg_node.rig_type == 'prop':
                    exported_bone_nodes.append(rg_node.export_bone_grp)
                    prop_bone_nodes.append(rg_node.export_bone_grp)
                    self.root_rig_dict[rg_node.export_bone_grp.name] = rg_node

        self.exported_bones.value = exported_bone_nodes
        if self.export_prop_origin.value:
            self.exported_origin_bones.value = prop_bone_nodes

    def export_anim_core(self):
        """export anim method"""
        if not self.exported_bones.value:
            cmds.warning('no root bone')
            return

        roots = [Node(root) for root in self.exported_bones.value]
        self.exported_roots = roots

        # set export parameter
        utils.anim_exp_sdk_parameter()

        cur_file_name = cmds.file(query=True, sceneName=True)
        base_name = os.path.basename(cur_file_name)
        dir_name = os.path.dirname(cur_file_name)

        min_time = cmds.playbackOptions(minTime=True, query=True)
        max_time = cmds.playbackOptions(maxTime=True, query=True)

        mel.eval('FBXExportBakeComplexStart -v {:d}'.format(int(min_time)))
        mel.eval('FBXExportBakeComplexEnd -v {:d}'.format(int(max_time)))

        for root in self.exported_roots:
            prefix_name = ''
            if root.name in self.root_rig_dict:
                split_text = root.fn_node.partialPathName().split('|')[-1]
                split_text = split_text.split(':')
                if len(split_text) >= 2:
                    prefix_name = split_text[-2]
            if not prefix_name:
                rg_node = self.root_rig_dict[root.name]
                prefix_name = rg_node.rig_type

            prefix_name = prefix_name.capitalize()

            exp_joints = cmds.ls(root, dag=True, type='joint')
            exp_joints = [Node(_jnt) for _jnt in exp_joints]

            # select joints
            cmds.select(exp_joints, replace=True)
            cmds.select(cmds.ls(type='HIKCharacterNode'), add=True)

            if self.exported_origin_bones.value:
                if root.name in self.exported_origin_bones.value:
                    if Node.object_exist(root.name):
                        _grp_node = Node(root.name)
                        if _grp_node.type_name == 'transform':
                            root_bones = _grp_node.get_children(type_='joint') or []
                            if root_bones:
                                utils.delete_key_freeze(root_bones[0])

            non_exp_nodes = utils.deselect_non_export() or []
            non_exp_nodes = [node.split(':')[-1] for node in non_exp_nodes]

            ext = re.search(r'.*(\.\D+)', base_name).group(1)

            exp_folder = '{0}/FBX_Anim/{1}'.format(dir_name, prefix_name)
            if not os.path.exists(exp_folder):
                os.makedirs(exp_folder)

            save_path = '{0}/{1}'.format(exp_folder,
                                         base_name.replace(ext, '.fbx'))
            mel.eval('FBXExport -f \"{0}\" -s'.format(save_path))

            fbx_file = FBX_Class(save_path)

            nodes = [node.name for node in root.get_parent_hierarchy()]
            fbx_file.unparent_nodes(parent_nodes=nodes)
            fbx_file.remove_namespace()

            if not self.export_end.value:
                fbx_file.remove_nodes_by_names(non_exp_nodes)

            fbx_file.save_scene_file()

        cmds.file(new=True, force=True)


class Areana_ExportAnimation(ExportAnimation):
    """
    Areana Export Animation
    """
    # --- input parameters

    @pa.bool_param(default=True)
    def export_body(self):
        """If True, enable body animation export."""

    @pa.bool_param(default=True)
    def export_face(self):
        """If True, enable face animation export."""

    @pa.bool_param(default=True)
    def export_prop(self):
        """If True, enable prop(weapon) animation export."""

    @pa.bool_param(default=True)
    def export_prop_origin(self):
        """If True, export prop(weapon) at origin point."""

    def run(self):
        """
        Areana Export run method
        """
        super(Areana_ExportAnimation, self).run()

        # set self.exported_bones.value
        exported_rig_nodes = []
        prop_rig_nodes = []
        if self.export_body.value:
            exported_rig_nodes += cmds.ls(type='CGameBodyTransform') or []

        if self.export_prop.value:
            exported_rig_nodes += cmds.ls(type='CGamePropTransform') or []
            prop_rig_nodes = cmds.ls(type='CGamePropTransform') or []

        if self.export_face.value:
            exported_rig_nodes += cmds.ls(type='CGameFaceTransform') or []

        exported_rig_nodes = [Node(rig_node) for rig_node in exported_rig_nodes]
        prop_rig_nodes = [Node(rig_node) for rig_node in prop_rig_nodes]

        roots = []
        for rig_node in exported_rig_nodes:
            exp_root = cmds.listConnections(rig_node.rootBone, source=True, destination=False, plugs=False) or []
            if exp_root:
                roots.append(exp_root[0])
        self.exported_bones.value = roots

        if self.export_prop_origin.value:
            props = []
            for rig_node in prop_rig_nodes:
                prop_root = cmds.listConnections(rig_node.rootBone, source=True, destination=False, plugs=False) or []
                if prop_root:
                    props.append(prop_root[0])
            self.exported_origin_bones.value = props
        else:
            self.exported_origin_bones.value = []
