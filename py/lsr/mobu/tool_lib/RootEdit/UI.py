from lsr.mobu.tool_lib.RootEdit.widget import RootEdit_Tool_Widget
from lsr.qt.core.widgets.qr_widgets.qr_window import get_qr_window_class


# get the base main window class
VERSION = "1.2"
base_class = get_qr_window_class(
    app_name="RootEdit Tool v{}".format(VERSION),
    qr_widget=RootEdit_Tool_Widget
)


class RootEdit_Tool_UI(base_class):
    """Anim Loop Tool UI."""
    _REUSE_SINGLETON = False

    def __init__(self):
        """ Creates and initializes this window. """
        self.__thread = None
        self.__save_path = None
        super(RootEdit_Tool_UI, self).__init__(set_style=False, banner_widget=True, has_art=True)

    def hideEvent(self, event):
        """ Save settings before hiding. """
        self.closeEvent(event)
