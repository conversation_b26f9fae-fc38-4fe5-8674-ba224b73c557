from functools import partial

import pyfbsdk as fb
from Qt import QtWidgets, QtCore

import lsr.mobu.rig.file_options as file_options
from lsr.qt.core.widgets import qr_widgets as QRWidget
from lsr.qt.core.widgets.files_list_widget import FilesListWidget
from lsr.mobu.tool_lib.MoEditTool.AnimTakeEdit import TakeEdit
from lsr.mobu.tool_lib.MoEditTool.SpeedCheck import CheckBoneSpeed
from lsr.mobu.tool_lib.MoEditTool.GenerateCurve import GenerateCurve
from lsr.mobu.tool_lib.RootEdit.root_edit import CGame_RootEdit
from lsr.mobu.utils.FindObjects import mobu_print


class RootEdit_Tool_Widget(QtWidgets.QWidget):
    """ Root Edit Tool Widget."""

    def __init__(self, *args, **kwargs):
        """ Creates and initializes this window. """
        super(RootEdit_Tool_Widget, self).__init__(*args, **kwargs)

        self.take_edit = TakeEdit()
        self.char_edit = CGame_RootEdit()
        self.check_bone_speed = CheckBoneSpeed()
        self.generate_curve = GenerateCurve()

        self.pos_axis_btn_dict = {}
        self.aim_axis_btn_dict = {}
        self.up_axis_btn_dict = {}

        # Create main layout
        self.main_layout = QtWidgets.QVBoxLayout(self)
        self.setLayout(self.main_layout)

        # Setup root position settings group
        self._setup_position_settings()
        self._add_separator_line()

        # Setup root rotation settings group
        self.rot_motion_checkbox = QRWidget.QRCheckBox("Rotation Settings")
        title_layout = QtWidgets.QHBoxLayout()
        title_layout.addWidget(self.rot_motion_checkbox)
        title_layout.addStretch()  # Push the checkbox to the left
        self.main_layout.addLayout(title_layout)
        self._setup_rotation_settings()

        self._add_separator_line()

        # Setup root motion controls
        self._setup_root_motion_controls()

        # Add spacing
        self.main_layout.addItem(QtWidgets.QSpacerItem(
            40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum))

        # Setup rotation controls
        self._setup_rotation_controls()

        # Add final spacing
        self.main_layout.addItem(QtWidgets.QSpacerItem(
            40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding))

        self._toggle_group_box_state(self.rot_motion_checkbox.isChecked())
        self.create_connections()

    def _add_separator_line(self):
        """Add a horizontal separator line to the layout."""
        line = QtWidgets.QFrame()
        line.setFrameShape(QtWidgets.QFrame.HLine)
        line.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.main_layout.addWidget(line)

    def _setup_rotation_settings(self):
        """Setup the root rotation aim settings group."""
        # Create group box
        self.root_rotation_grp_box = QtWidgets.QGroupBox(self)
        self.root_rotation_grp_box.setMaximumHeight(230)
        # self.root_rotation_grp_box.setTitle("Rotate Settings")
        self.main_layout.addWidget(self.root_rotation_grp_box)

        box_lay = QtWidgets.QVBoxLayout(self.root_rotation_grp_box)

        # Create button groups
        self.aim_axis_btn_box = QtWidgets.QButtonGroup()
        self.up_axis_btn_box = QtWidgets.QButtonGroup()

        # Add aim axis controls
        box_lay.addWidget(QtWidgets.QLabel("Aim Axis(Root Bone Local Axis)", self))
        self._add_axis_buttons(self.aim_axis_btn_dict, self.aim_axis_btn_box, box_lay, type_name="aim")

        # Add up axis controls
        box_lay.addWidget(QtWidgets.QLabel("Object Up Axis(axis of the root bone aligned with the world's upward axis)", self))
        self._add_axis_buttons(self.up_axis_btn_dict, self.up_axis_btn_box, box_lay, type_name="up")

    def _setup_position_settings(self):
        """Setup the root position settings group."""
        # Create group box
        self.root_position_grp_box = QtWidgets.QGroupBox(self)
        self.root_position_grp_box.setTitle("Position Settings(World Axis)")
        self.main_layout.addWidget(self.root_position_grp_box)

        # Add axis buttons
        box_lay = QtWidgets.QVBoxLayout(self.root_position_grp_box)

        # Add axis buttons
        btn_layout = QtWidgets.QHBoxLayout()

        for btn_names in ["x", "y", "z"]:
            for btn_name in btn_names:
                btn = QRWidget.QRCheckBox("{}_pos_cb".format(btn_name))
                btn.setText(btn_name)
                self.pos_axis_btn_dict[btn] = btn_name
                btn_layout.addWidget(btn)

        box_lay.addLayout(btn_layout)

    def _add_axis_buttons(self, btn_dict, btn_group, layout, type_name):
        """
        Add a set of axis selection radio buttons.

        Args:
            btn_dict(dict): A dictionary of buttons to add.
            btn_group(QButtonGroup): The button group to add the buttons to.
            layout(QVBoxLayout): The layout to add the buttons to.
        """
        plus_hlay = QtWidgets.QHBoxLayout()
        minus_hlay = QtWidgets.QHBoxLayout()

        axis_ids = {"x": 0, "y": 1, "z": 2, "-x": 3, "-y": 4, "-z": 5}

        for btn_names, btn_layout in zip(
                [["x", "y", "z"], ["-x", "-y", "-z"]],
                [plus_hlay, minus_hlay]
        ):
            for btn_name in btn_names:
                btn = QRWidget.QRRadioButton("{}_{}".format(btn_name, type_name))
                btn.setText(btn_name)
                btn_dict[btn] = btn_name
                btn_group.addButton(btn, id=axis_ids[btn_name])
                btn_layout.addWidget(btn)
            layout.addLayout(btn_layout)

    def _setup_root_motion_controls(self):
        """Setup the root motion controls."""
        root_hbox = QtWidgets.QHBoxLayout()

        # Add checkboxes
        self.og_CB = QRWidget.QRCheckBox("Keep on Ground")
        self.og_CB.setChecked(True)
        root_hbox.addWidget(self.og_CB)

        self.fs_CB = QRWidget.QRCheckBox("Follow Selected")
        root_hbox.addWidget(self.fs_CB)

        # Set stretch factors
        for i in range(2):
            root_hbox.setStretch(i, 1)

        self.main_layout.addLayout(root_hbox)

        self._add_separator_line()

        self.root_CB = QRWidget.QRCheckBox("All Takes")
        self.main_layout.addWidget(self.root_CB)

        self.file_list_widget = FilesListWidget()
        self.main_layout.addWidget(self.file_list_widget)

        self.root_btn = QtWidgets.QPushButton("Create Root Motion")
        self.root_btn.setFixedHeight(30)
        self.main_layout.addWidget(self.root_btn)

    def _setup_rotation_controls(self):
        """Setup the rotation and repositioning controls."""
        # Create rotation controls

        # add group box
        self.fix_grp_box = QtWidgets.QGroupBox(self)
        self.fix_grp_box.setTitle("Fix Rotation / Position")
        self.main_layout.addWidget(self.fix_grp_box)
        # Add layout
        vbox = QtWidgets.QVBoxLayout(self.fix_grp_box)

        self.fix_hbox = QtWidgets.QHBoxLayout()

        degree_label = QtWidgets.QLabel("Degree")
        self.fix_hbox.addWidget(degree_label)

        self.degree_DSB = QtWidgets.QDoubleSpinBox(minimum=-9999, maximum=9999)
        self.degree_DSB.setSingleStep(15)
        self.fix_hbox.addWidget(self.degree_DSB)

        self.degree_btn = QtWidgets.QPushButton("Rotate Character")
        self.fix_hbox.addWidget(self.degree_btn)

        # Set stretch factors
        for i, factor in enumerate([1, 1, 3]):
            self.fix_hbox.setStretch(i, factor)

        vbox.addLayout(self.fix_hbox)

        # Add reposition button
        self.rep_btn = QtWidgets.QPushButton("Re-Position To Zero of Character Space")
        vbox.addWidget(self.rep_btn)

    def create_connections(self, *args, **kwargs):
        """create connections for all buttons"""
        # root edit part
        self.degree_btn.clicked.connect(partial(self.rotate_root))
        self.rep_btn.clicked.connect(partial(self.reposition_root))
        self.root_btn.clicked.connect(partial(self.create_root_motion))
        self.rot_motion_checkbox.stateChanged.connect(partial(self._toggle_group_box_state))

        # self.animationFiles_lw.itemDoubleClicked.connect(partial(self.item_double_clicked))
        self.file_list_widget.animationFiles_lw.itemDoubleClicked.connect(partial(self.item_double_clicked))

    def _toggle_group_box_state(self, state):
        """Slot to enable/disable all components in the group box based on the checkbox state."""
        if state == QtCore.Qt.Checked:
            self.root_rotation_grp_box.setEnabled(True)
        else:
            self.root_rotation_grp_box.setEnabled(False)

    def rotate_root(self, *args, **kwargs):
        """rotate the root of the character"""
        self.char_edit = CGame_RootEdit()
        self.char_edit.fix_char_root_rotation(self.degree_DSB.value())

    def reposition_root(self, *args, **kwargs):
        """ reposition the character """
        self.char_edit = CGame_RootEdit()
        self.char_edit.char_reposition()

    def create_root_motion(self, *args, **kwargs):
        """
        Create root motion for the character.

        Uses the current UI checkbox states and axis settings to configure the root motion.
        """
        # Get selected axis values before function call for better readability
        selected_pos_axis = [value for btn, value in self.pos_axis_btn_dict.items() if btn.isChecked()]
        try:
            selected_aim_axis = next(value for btn, value in self.aim_axis_btn_dict.items() if btn.isChecked())
            selected_up_axis = next(value for btn, value in self.up_axis_btn_dict.items() if btn.isChecked())

            for btn, value in self.aim_axis_btn_dict.items():
                if value == "-y":
                    btn.setChecked(True)
                    break
            for btn, value in self.up_axis_btn_dict.items():
                if value == "z":
                    btn.setChecked(True)
                    break

        except BaseException:
            selected_aim_axis = "-y"
            selected_up_axis = "z"

        if not self.file_list_widget.apply_multiple_cb.isChecked():
            self._process_single_file(
                is_create_to_all=self.root_CB.isChecked(),
                bFollowSelected=self.fs_CB.isChecked(),
                bIgnoreY=self.og_CB.isChecked(),
                create_rotation=self.rot_motion_checkbox.isChecked(),
                aim_axis=selected_aim_axis,
                up_axis=selected_up_axis,
                pos_axis=selected_pos_axis
            )
        else:
            self._process_multiple_files(
                is_create_to_all=self.root_CB.isChecked(),
                bFollowSelected=self.fs_CB.isChecked(),
                bIgnoreY=self.og_CB.isChecked(),
                create_rotation=self.rot_motion_checkbox.isChecked(),
                aim_axis=selected_aim_axis,
                up_axis=selected_up_axis,
                pos_axis=selected_pos_axis
            )

    def item_double_clicked(self, *args, **kwargs):
        """ Open file when double clicked """
        item = self.file_list_widget.animationFiles_lw.selectedItems()[0]
        file_path = str(item.text())
        app = fb.FBApplication()
        app.FileOpen(file_path, False)

    def _process_single_file(self, *args, **kwargs):
        """ process single file """
        self.char_edit.create_char_root_motion(**kwargs)
        fb.FBSystem().Scene.Evaluate()

    def _process_multiple_files(self, *args, **kwargs):
        """ process multiple files"""
        app = fb.FBApplication()
        for i in range(self.file_list_widget.animationFiles_lw.count()):
            try:
                item = self.file_list_widget.animationFiles_lw.item(i)
                file_path = str(item.text())
                app.FileOpen(file_path, False)

                self.char_edit.create_char_root_motion(**kwargs)

                fb.FBSystem().Scene.Evaluate()
                save_options = file_options.SaveAllOption(False)
                app.FileSave(file_path, save_options)
            except Exception as e:
                print("Error processing file:", e)