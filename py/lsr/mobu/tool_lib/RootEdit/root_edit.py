import pyfbsdk as fb

from lsr.mobu.tool_lib.MoEditTool import matrix_math
from lsr.mobu.nodezoo.node import Node
from lsr.mobu.nodezoo.node import Character
from lsr.mobu.nodezoo.node import ParentConstraint
from lsr.mobu.nodezoo.node import AimConstraint
from lsr.mobu.nodezoo.node import PointConstraint
from lsr.mobu.utils.FindObjects import clear_selection
from lsr.mobu.utils.PlaybackControl import get_time_range, set_current_frame, get_current_frame
from lsr.mobu.utils import anim_util
import lsr.mobu.mobu_const as mobu_const
from lsr.mobu.tool_lib.MoEditTool.CharacterEdit import CharEdit
from lsr.mobu.utils.FindObjects import mobu_print


class CGame_RootEdit(CharEdit):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def create_root_motion(self, obj, ignore_y, create_rotation, aim_axis, up_axis, *args, **kwargs):
        """
        override the create_root_motion function

        Args:
            obj (object): Reference Object
            ignore_y (bool): True or False
            create_rotation (bool): True or False
            aim_axis (str): string, "x", "y", "z" or "-x", "-y", "-z"
            up_axis (str): string, "x", "y", "z" or "-x", "-y", "-z"

        Returns:
            bool: True if successful, False if not
        """

        if not self.is_char_valid():
            return False

        # check the obj type:
        if not issubclass(type(obj), fb.FBModel):
            print("Error: The reference object must be a kind of FBModel.")
            return False

        mobu_print("Create Root Motion for CGAME...")
        time_range = get_time_range()
        current_frame = int(get_current_frame())

        char = Character.current_character()

        # deal with the position
        all_data = matrix_math.get_matrices_from_time(char.all_skeletons)
        matrix_data = {}
        for node in [char.hip_bone.fb_node, char.root_bone.fb_node]:
            if node in [n.fb_node for n in all_data.keys()]:
                z_node = list(all_data.keys())[[n.fb_node for n in all_data.keys()].index(node)]
                matrix_data[z_node] = all_data[z_node]
                del all_data[z_node]

        height_0_values = [0.0 for _ in range(len(matrix_data[char.hip_bone]))]

        height_values = []
        if ignore_y:
            height_values = [0.0 for _ in range(len(matrix_data[char.hip_bone]))]
        else:
            root_start_y = matrix_data[char.root_bone][0].matrix[13]
            hip_start_y = matrix_data[char.hip_bone][0].matrix[13]
            diff_start_y = hip_start_y - root_start_y
            height_values.append(0.0)

            for i in range(1, len(matrix_data[char.hip_bone])):
                root_y = matrix_data[char.root_bone][i].matrix[13]
                hip_y = matrix_data[char.hip_bone][i].matrix[13]
                diff_y = hip_y - root_y
                height_values.append(diff_y - diff_start_y)

        hip_data = matrix_math.change_matrices_height(matrix_data[char.hip_bone], height_values=height_0_values)
        root_data = matrix_data[char.root_bone]
        matrix_math.set_matrices_from_time(
            [char.root_bone],
            [hip_data],
            self_data=root_data,
            *args, **kwargs)

        # end deal with the position

        # prepare aim constraint
        set_current_frame(time_range[0])
        fb.FBSystem().Scene.Evaluate()

        root_loc_name = "TEMP_ROOT_LOC"
        if Node.object_exist(root_loc_name):
            Node(root_loc_name).delete()
        root_loc = Node.create("FBModelNull", name=root_loc_name)

        aim_loc_name = "TEMP_AIM_LOC"
        if Node.object_exist(aim_loc_name):
            Node(aim_loc_name).delete()
        aim_loc = Node.create("FBModelNull", name=aim_loc_name)
        aim_loc.parent = char.root_bone
        # aim_loc.set_rotation((0, 0, 0), space="object")
        aim_loc.set_translation((0, -10, 0), space="object")
        fb.FBSystem().Scene.Evaluate()
        aim_loc.parent = None
        fb.FBSystem().Scene.Evaluate()


        hip_ctrls = char.hip_bone
        root_micro_ctrl = char.root_bone

        if not hip_ctrls:
            root_loc.delete()
            aim_loc.delete()
            return

        aim_const_node = AimConstraint.create(
            name="Aim_{}".format(aim_loc.name)
        )

        pc_const_node = ParentConstraint.create(
            name="Parent_{}".format(aim_loc.name)
        )

        # set root_loc and after constraint
        hip_ctrl = hip_ctrls
        root_loc.parent = hip_ctrl
        fb.FBSystem().Scene.Evaluate()
        root_loc.set_rotation((0, 0, 0), space="object")
        fb.FBSystem().Scene.Evaluate()

        if create_rotation:
            pc_const_node.add_reference(0, aim_loc.fb_node)
            pc_const_node.add_reference(1, root_loc.fb_node)
            # if ignore_y:
            pc_const_node.attr("Affect Translation Y").value = False
            pc_const_node.attr("Affect Rotation Y").value = False
            pc_const_node.snap()
            pc_const_node.active = True
            pc_const_node.lock = True

            aim_vec = mobu_const.AXIS_VEC_DICT[aim_axis]
            up_vec = mobu_const.AXIS_VEC_DICT[up_axis]
            world_up_vector = mobu_const.AXIS_VEC_DICT["y"]

            aim_const_node.add_reference(0, root_micro_ctrl.fb_node)
            aim_const_node.add_reference(1, aim_loc.fb_node)
            aim_const_node.aim_vector = aim_vec
            aim_const_node.world_up_vector = world_up_vector
            aim_const_node.up_vector = up_vec
            aim_const_node.world_up_type = 3
            # do not snap, maintain offset
            aim_const_node.active = True
            aim_const_node.lock = True

        # prepare position constraint
        pos_const_node = PointConstraint.create(
            root_micro_ctrl, root_loc,
            name="Position_{}".format(root_loc.name),
            maintain_offset=False,
            lock=True,
            active=True
        )

        fb.FBSystem().Scene.Evaluate()

        # get all keyframes
        pos_list = list()
        rot_list = list()

        for i in range(time_range[0], time_range[1] + 1):
            time = fb.FBTime(0, 0, 0, i)
            fb.FBPlayerControl().Goto(time)
            pos = root_micro_ctrl.get_translation(space="object")
            rot = root_micro_ctrl.get_rotation(space="object")
            pos_list.append(pos)
            rot_list.append(rot)

        for frame, pos, rot in zip(range(time_range[0], time_range[1] + 1), pos_list, rot_list):
            time = fb.FBTime(0, 0, 0, frame)
            fb.FBPlayerControl().Goto(time)
            anim_util.add_keys_on_node(root_micro_ctrl, "Lcl Translation", time, list(pos))
            if create_rotation:
                anim_util.add_keys_on_node(root_micro_ctrl, "Lcl Rotation", time, list(rot))

        clear_selection()
        root_micro_ctrl.selected = True
        currentTake = fb.FBSystem().CurrentTake
        options = fb.FBPlotOptions()
        options.EvaluateDeformation = True
        currentTake.PlotTakeOnSelected(options)

        aim_const_node.delete()
        pos_const_node.delete()
        pc_const_node.delete()
        aim_loc.delete()
        root_loc.delete()

        if not ignore_y:
            interpolation_type = fb.FBInterpolation.kFBInterpolationLinear
            modify_property = root_micro_ctrl.fb_node.PropertyList.Find("Lcl Translation")
            pos_anim_nodes = modify_property.GetAnimationNode().Nodes

            for i, time_int in enumerate(range(time_range[0], time_range[1] + 1)):
                time_instance = fb.FBTime(0, 0, 0, time_int)
                pos_anim_nodes[1].KeyAdd(time_instance, height_values[i], interpolation_type)

        set_current_frame(current_frame)
        fb.FBSystem().Scene.Evaluate()

        return True
