"""
This module holds thread relatives
"""
import os.path
import threading
import time
from functools import partial

# Studio package module imports
from Qt import Qt<PERSON>ore, QtGui, QtWidgets

# Tool package module imports
from lsr.anim_lib.manager.cache import CacheManager
from lsr.anim_lib.manager.signal import SignalManager
from lsr.anim_lib.utility.utils import get_linked_paths
from lsr.anim_lib.data import constant
from lsr.anim_lib.utility.utils import get_subfolders_containing_string
# from lsr.anim_lib.team_work_action.git.controller import GIT_ACTION_INSTANCE

class QIconWorker(QtCore.QRunnable):
    """A worker class for generating library [flipbook item cover] as qimage"""
    # cache_manager = CacheManager()

    def __init__(self):
        QtCore.QRunnable.__init__(self)
        self._image_path = None
        self.id = None
        self.if_cache = None

    def set_path(self, image_path, id, start_cache=False, *args, **kwargs):
        self._image_path = image_path
        self.id = id
        self.if_cache = start_cache

    def run(self, *args, **kwargs):
        pixmap = QtGui.QPixmap(self._image_path)
        if kwargs['scalable']:
            if constant.PREVIEW_ICON_SIZE_VALUE:
                pixmap = pixmap.scaled(QtCore.QSize(constant.PREVIEW_ICON_SIZE_VALUE,
                                                    constant.PREVIEW_ICON_SIZE_VALUE))
        self.id._frames = []
        self.id._frames.append(pixmap)
        # self.id._frames.append(qimage)
        self.id._current_display = self.id._frames[0]
        # if self.if_cache:
            # self.cache_manager.cache_dict[self._image_path] = qimage




def GitStatusWatchDog(*args, **kwargs):
    t = threading.currentThread()
    while getattr(t, "do_run", True):
        try:
            if not getattr(t, "do_pause", True):
                if constant.INSTANCE_TEAM_ACTION_CTRL.current_repo:
                    constant.KEYPATH_GIT_REMOTE_UPDATED_FOOTAGES = constant.INSTANCE_TEAM_ACTION_CTRL.get_remote_dirty_folder()
                    constant.KEYPATH_GIT_REMOTE_UPDATED_ITEMS = [os.path.normpath(x) for x in
                                                                 get_linked_paths(constant.KEYPATH_GIT_REMOTE_UPDATED_FOOTAGES,
                                                                                  constant.INSTANCE_TEAM_ACTION_CTRL.current_repo)]
                    raw_dirty_folders = constant.INSTANCE_TEAM_ACTION_CTRL.get_local_dirty_folder()
                    constant.KEYPATH_GIT_LOCAL_UPDATED_FOOTAGES = []
                    for folder in raw_dirty_folders:
                        constant.KEYPATH_GIT_LOCAL_UPDATED_FOOTAGES.append(folder)
                        if "_footage" not in folder:
                            footage_folders = get_subfolders_containing_string(os.path.join(constant.INSTANCE_TEAM_ACTION_CTRL.current_repo, folder), "_footage")
                            for i in footage_folders:
                                constant.KEYPATH_GIT_LOCAL_UPDATED_FOOTAGES.append(i)
                    constant.KEYPATH_GIT_LOCAL_UPDATED_ITEMS = [os.path.normpath(x) for x in
                                                                 get_linked_paths(constant.KEYPATH_GIT_LOCAL_UPDATED_FOOTAGES,
                                                                                  constant.INSTANCE_TEAM_ACTION_CTRL.current_repo)]
                    SignalManager.git_pull_all_label_updated.emit(len(constant.KEYPATH_GIT_REMOTE_UPDATED_ITEMS))
                    SignalManager.git_push_all_label_updated.emit(len(constant.KEYPATH_GIT_LOCAL_UPDATED_ITEMS))
        except Exception as error:
            print("[GitStatusWatchDog Error], %s" % error)
            setattr(t, "do_run", False)
        finally:
            time.sleep(0.5)


def FlipbookItemStatusWatchDog(*args, **kwargs):
    t = threading.currentThread()
    while getattr(t, "do_run", True):
        try:
            if not getattr(t, "do_pause", True):
                if constant.INSTANCE_TEAM_ACTION_CTRL.current_repo:
                    for footage in constant.KEYPATH_GIT_LOCAL_UPDATED_FOOTAGES:
                        item = constant.INSTANCE_FLIPBOOK_MODEL.get_item_by_path(footage)
                        if item:
                            if "dirty_local" not in item.io_data["flipbook_tags"]:
                                item.io_data["flipbook_tags"].append("dirty_local")

                    for footage in constant.KEYPATH_GIT_REMOTE_UPDATED_FOOTAGES:
                        item = constant.INSTANCE_FLIPBOOK_MODEL.get_item_by_path(footage)
                        if item:
                            if "dirty_remote" not in item.io_data["flipbook_tags"]:
                                item.io_data["flipbook_tags"].append("dirty_remote")
        except Exception as error:
            print("[FlipbookItemStatusWatchDog Error], %s" % error)
            setattr(t, "do_run", False)
        finally:
            time.sleep(0.5)


class ThreadManager(QtCore.QObject):
    """
    The class is designed to manage all thread in Anim Lib
    """

    thread_pool = []

    def __int__(self, *args, **kwargs):
        super(ThreadManager, self).__int__(*args, **kwargs)

    @classmethod
    def init(cls, *args, **kwargs):
        git_fetch_thread = threading.Thread(target=GitStatusWatchDog, args=())
        preview_model_update_thread = threading.Thread(target=FlipbookItemStatusWatchDog, args=())
        cls.thread_pool.append(git_fetch_thread)
        cls.thread_pool.append(preview_model_update_thread)
        for thd in cls.thread_pool:
            thd.start()

    @classmethod
    def pause(cls, *args, **kwargs):
        for thd in cls.thread_pool:
            thd.do_pause = True

    @classmethod
    def resume(cls, *args, **kwargs):
        for thd in cls.thread_pool:
            thd.do_pause = False

    @classmethod
    def terminated_all_threads(cls, *args, **kwargs):
        for thd in cls.thread_pool:
            thd.do_run = False
        cls.thread_pool = []