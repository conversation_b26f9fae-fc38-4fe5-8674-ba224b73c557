"""
This is the main entry of animation library.
"""
# python package
import os

# Studio package module imports
import lsr.qt.core.base_main_window as main_window
from Qt import QtWidgets
from lsr.anim_lib.data import constant
from lsr.anim_lib.ui.main_view import AnimLibMain<PERSON> as anim_lib_ui
from lsr.anim_lib.utility import utils as uti_common
from lsr.anim_lib.manager.signal import SignalManager
from lsr.anim_lib.manager.thread import ThreadManager
from lsr.anim_lib.manager.translator import TranslateManager


base_class = main_window.get_window_class(app_name="LSR Anim Lib v1.0")

class Window(base_class):

    def setup_ui(self, *args, **kwargs):
        constant.INSTANCE_WINDOW_ROOT = self
        constant.INSTANCE_UI_TRANSLATOR = TranslateManager()
        if "git":
            from lsr.anim_lib.team_action.git.controller import GitActionController
            constant.INSTANCE_TEAM_ACTION_CTRL = GitActionController()
            try:
                constant.INSTANCE_TEAM_ACTION_CTRL.current_repo = constant.KEYPATH_CURRENT_GIT_LOCAL_REPO
            except Exception as e:
                print(e)

        central_widget = QtWidgets.QWidget()
        main_layout = QtWidgets.QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        central_widget.setLayout(main_layout)
        self.setLayout(main_layout)
        self.setCentralWidget(central_widget)

        self.main_ui = anim_lib_ui(self)
        main_layout.addWidget(self.main_ui)

        soft_title = uti_common.get_current_program_title()
        if soft_title:
            app_name = "LSR Anim Library   ---  %s" % soft_title
        self.setWindowTitle(app_name)

    def load_settings(self, *args, **kwargs):
        settings = super(Window, self).load_settings()
        settings.beginGroup('main_window')
        start_up_path = settings.value(
            'start_up_path', constant.KEYPATH_TOOL_DATA)
        if not os.path.isdir(start_up_path):
            # if user create a path, tool save it to disk, and then user delete it,
            # next time will reset back to default on open
            if not os.path.isdir(constant.KEYPATH_TOOL_DATA):
                os.makedirs(constant.KEYPATH_TOOL_DATA)
            start_up_path = constant.KEYPATH_TOOL_DATA
        constant.KEYPATH_TOOL_DATA = start_up_path
        self.main_ui.left_section_folder.slot_init_root_path(start_up_path)

        if not settings.value("start_up_splitter"):
            settings.endGroup()
            return settings

        start_up_splitter_sizes = [int(x) for x in settings.value('start_up_splitter', [])]
        self.main_ui.splitter.setSizes(start_up_splitter_sizes)
        settings.endGroup()

        self.main_ui.right_section_edit.load_settings()
        self.main_ui.left_section_folder.load_settings()
        self.main_ui.center_section_preview.load_settings()
        self.main_ui.bottom_section_status.load_settings()
        settings = constant.INSTANCE_SUBUI_APPLY_SETTING.load_settings()

        return settings

    def save_settings(self, *args, **kwargs):
        settings = super(Window, self).save_settings()
        settings.beginGroup('main_window')
        settings.setValue('start_up_path', constant.KEYPATH_TOOL_DATA)
        settings.setValue('start_up_splitter', self.main_ui.splitter.sizes())
        settings.endGroup()
        settings.sync()

        self.main_ui.right_section_edit.save_settings()
        self.main_ui.left_section_folder.save_settings()
        self.main_ui.center_section_preview.save_settings()
        self.main_ui.bottom_section_status.save_settings()
        settings = constant.INSTANCE_SUBUI_APPLY_SETTING.save_settings()

        return settings

    def showEvent(self, event):
        """Save settings before showing."""
        super(Window, self).showEvent(event)

        window_geo = self.frameGeometry()

        screen = QtWidgets.QApplication.screenAt(self.pos())
        if not screen:
            screen = QtWidgets.QApplication.primaryScreen()

        screen_geo = screen.availableGeometry()

        if not screen_geo.contains(window_geo):
            center_x = screen_geo.center().x() - window_geo.width() // 2
            center_y = screen_geo.center().y() - window_geo.height() // 2
            self.move(center_x, center_y)

        # ThreadManager.init()
        # ThreadManager.resume()

    def closeEvent(self, event):
        super(Window, self).closeEvent(event)
        SignalManager.close_all_window.emit()
        # ThreadManager.terminated_all_threads()


def launch():

    Window.launch()
