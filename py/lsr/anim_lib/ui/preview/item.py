"""
This module holds the preview flipbook item and its operation
"""
import json
import shutil
import os
from collections import OrderedDict
from functools import partial

# Studio package module imports
from lsr.anim_lib.manager import <PERSON><PERSON>ana<PERSON>, CacheManager
from lsr.anim_lib.manager.thread import QIconWorker
from lsr.anim_lib.data.base_class import ImageContainer
from Qt import QtGui, QtWidgets, QtCore
from lsr.anim_lib.data import constant


class AnimLibFlipbookItem(ImageContainer):
    """ Flipbook item for preview """
    cache_manager = CacheManager()

    def __init__(self, io_data=constant.STRUCTURE_FLIPBOOK_DATA):
        super(AnimLibFlipbookItem, self).__init__(None)
        self.io_data = io_data
        self._lock_load_qimage = False
        self._current_display = None
        self.animation_cache = None
        self.annotation_cache = OrderedDict()
        self._index = 0
        self.scalable = False

        self.frameChanged.connect(partial(self.slot_update_flipbook_cover))

    def reset(self, *args, **kwargs):
        if not self._timer:
            self._timer = QtCore.QTimer(self.parent())
            self._timer.setSingleShot(False)
            self._timer.timeout.connect(partial(self.slot_frameChanged))
        if not self._paused:
            self._frame = 0
        self._timer.stop()

    def pause(self, *args, **kwargs):
        self._paused = True
        self._timer.stop()

    def resume(self, *args, **kwargs):
        if self._paused:
            self._paused = False
            self._timer.start()

    def stop(self, *args, **kwargs):
        self._timer.stop()
        self._frame = 0
        self._current_display = self._frames[0]
        self._percent = 1

    def start(self, *args, **kwargs):
        self.reset()
        if self._timer:
            self._timer.start(1000.0 / self._fps)

    def load_thumbNail(self, *args, **kwargs):
        if self.io_data["flipbook_thumbnail_file"]:
            worker = QIconWorker()
            worker.setAutoDelete(1)
            worker.set_path(self.io_data["flipbook_thumbnail_file"], self, start_cache=True)
            worker.run(scalable=self.scalable)

    def load_flipbook(self, *args, **kwargs):
        if self.io_data["flipbook_sequence_folder"]:
            if not self._lock_load_qimage:
                image_paths = []
                for file in os.listdir(self.io_data["flipbook_sequence_folder"]):
                    if file.endswith(".jpg"):
                        image_paths.append(os.path.join(self.io_data["flipbook_sequence_folder"], file))

                for image_path in image_paths[1:]:
                    pixmap = QtGui.QPixmap(image_path)
                    if self.scalable:
                        if constant.PREVIEW_ICON_SIZE_VALUE:
                            pixmap = pixmap.scaled(constant.PREVIEW_ICON_SIZE_VALUE,
                                                   constant.PREVIEW_ICON_SIZE_VALUE)
                    # qicon = QtGui.QIcon(pixmap)
                    # self._frames.append(qicon)
                    self._frames.append(pixmap)
                self._lock_load_qimage = True

    def load_animation_data(self, *args, **kwargs):
        pass
        # Todo, check to load animation fbx
        anim_data_path = self.io_data["animation_data_file"]
        if not os.path.isfile(anim_data_path):
            return
        with open(anim_data_path, 'r') as f:
            self.animation_cache = json.load(f)

    def load_annotation_json(self, *args, **kwargs):
        if self.io_data["annotation_data_file"]:
            with open(os.path.normpath(self.io_data["annotation_data_file"]), "r") as f:
                raw = json.load(f)
                for key in constant.STRUCTURE_ANNOTATION:
                    if key in raw:
                        self.annotation_cache[key] = raw[key]
                    else:
                        self.annotation_cache[key] = constant.STRUCTURE_ANNOTATION[key]

    def slot_update_flipbook_cover(self, frame, *args, **kwargs):
        self._current_display = self._frames[frame]
        self.update_percent()
        SignalManager.model_item_decorate_data.emit(self._index)

    def slot_frameChanged(self, *args, **kwargs):
        """
        Triggered when the current frame changes.

        :rtype: None
        """
        if not self._frames:
            return

        frame = self._frame
        frame += 1
        self.jumpToFrame(frame)

    def play_flipbook_animation(self, index=None, *args, **kwargs):
        self.start()
        # given self's index at parent view to info the view to updated
        self._index = index

    def jump_flipbook_animation(self, parent=None, index=None, *args, **kwargs):
        if not self._paused:
            self.pause()
        self._index = index
        self.jumpToCurSorFrame(parent, self._index)

    def get_item_display_name(self, *args, **kwargs):
        if self.io_data["flipbook_sequence_folder"]:
            name = os.path.basename(self.io_data["flipbook_sequence_folder"]).replace("_footage", "")
            if any(key_str in name for key_str in ["_anim", "_pose"]):
                name = name.rpartition("_")[0]
            return name

    def get_item_full_name(self, *args, **kwargs):
        if self.io_data["flipbook_sequence_folder"]:
            return self.io_data["flipbook_sequence_folder"]

    def jumpToFrame(self, frame, *args, **kwargs):
        """
        Set the current frame.

        :rtype: int or None
        """
        if frame >= self.frameCount():
            frame = 0
        self._frame = frame
        self.frameChanged.emit(frame)

    def jumpToCurSorFrame(self, parent, index, *args, **kwargs):
        """
        caculate the [frame number] and [_percent] base on current cursor positon.
        only used this in a qabstract item view

        this is a convient wrapper for a child item to call its parent method

        input:
            parent : parent view instance
            index : self's index in parent view

        :rtype: int or None
        """
        local_rect = parent.visualRect(index)
        cursor_pos_x = parent.mapFromGlobal(QtGui.QCursor.pos()).x()
        percent = ((cursor_pos_x - local_rect.topLeft().x()) / float(local_rect.width())) # cast to float for py2
        frame = int(self.frameCount() * percent)
        self.jumpToFrame(frame)
        SignalManager.model_item_decorate_data.emit(self._index)

    def destruct(self, *args, **kwargs):
        if os.path.isdir(self.io_data["flipbook_sequence_folder"]):
            shutil.rmtree(self.io_data["flipbook_sequence_folder"])
        del self


