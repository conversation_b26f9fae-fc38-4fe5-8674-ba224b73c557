"""
flipbook view base class
"""
from functools import partial

from Qt import QtWidgets, QtCore, QtGui


class AnimBrowserViewBase(QtWidgets.QListView):
    """anim-lib flipbook ui"""
    def __init__(self, parent=None):
        super(AnimBrowserViewBase, self).__init__(parent)
        self._init_layout()
        self._customized_view()
        self._do_signal_slot_connection()

    def _init_layout(self, *args, **kwargs):
        self._current_under_mouse_item_index = None
        self._current_under_mouse_item = None
        self._cursor_pos = None
        self._lock_allow_playing_flipbook = True

        self._user_wait_timer = QtCore.QTimer()
        self._user_wait_timer.setSingleShot(False)
        self._user_wait_timer.setInterval(400)

    def _customized_view(self, *args, **kwargs):
        # icon view
        self.setViewMode(QtWidgets.QListView.IconMode)
        self.setFlow(QtWidgets.QListView.LeftToRight)
        self.setResizeMode(QtWidgets.QListView.Adjust)
        self.setUniformItemSizes(True)

        # scroll bar
        self.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        # layout
        self.setContentsMargins(0, 0, 0, 0)

        # feature
        self.setMouseTracking(1)
        self.setSelectionMode(QtWidgets.QListView.NoSelection)

    def _do_signal_slot_connection(self, *args, **kwargs):
        self._user_wait_timer.timeout.connect(partial(self.slot_init_flipbook_item_animation))

    def _is_control_pressed(self, *args, **kwargs):
        modifiers = QtWidgets.QApplication.keyboardModifiers()
        return modifiers == QtCore.Qt.ControlModifier

    def _get_under_cursor_itme_index(self, *args, **kwargs):
        return self.indexAt(self.mapFromGlobal(QtGui.QCursor.pos()))

    # move all this to widget
    def _get_under_cursor_itme(self, *args, **kwargs):
        index = self._get_under_cursor_itme_index()
        item = None
        if not index.isValid():
            return item
        item = self.model().flipbook_items[index.row()]
        return item

    def get_given_pos_item(self, pos, *args, **kwargs):
        index = self.indexAt(pos)
        item = None
        if not index.isValid():
            return item
        item = self.model().flipbook_items[index.row()]
        return item

    def leaveEvent(self, event):
        if self._user_wait_timer:
            self._user_wait_timer.stop()
            if self.model().flipbook_items:
                self.model().stop_playing_all_items()

    def mouseMoveEvent(self, event):
        if not self._is_control_pressed():
            # check if the item is alread playing but paused
            new_item_index = self._get_under_cursor_itme_index()
            if new_item_index.isValid():
                new_pose = QtGui.QCursor.pos()
                # check if user move cursor
                if new_pose != self._cursor_pos:
                    self._user_wait_timer.stop()

                # stop preview playing item
                self._lock_allow_playing_flipbook = False
                self._user_wait_timer.start()
                self._cursor_pos = new_pose
            else:
                self._user_wait_timer.stop()
                self.model().stop_playing_all_items()
                self._lock_allow_playing_flipbook = False

        else:
            self._user_wait_timer.stop()
            self.model().stop_playing_all_items()
            index = self._get_under_cursor_itme_index()
            if index.isValid():
                item = self._get_under_cursor_itme()
                if item._lock_load_qimage == True:
                    item.jump_flipbook_animation(parent=self, index=index)

    def wheelEvent(self, event):
        self._user_wait_timer.stop()
        return super(AnimBrowserViewBase, self).wheelEvent(event)

    def slot_init_flipbook_item_animation(self, *args, **kwargs):
        index = self._get_under_cursor_itme_index()
        if index.isValid():
            if not self._lock_allow_playing_flipbook:
                item = self._get_under_cursor_itme()
                if not item.get_item_display_name() == "anim_lib_tool":
                    item.load_flipbook()
                    item.play_flipbook_animation(index)
                    self._lock_allow_playing_flipbook = True
    # TODO 看播放的时候为什么会吃内存
